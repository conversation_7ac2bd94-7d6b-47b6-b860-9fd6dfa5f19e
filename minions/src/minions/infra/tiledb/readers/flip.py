from minions.infra.tiledb.axes import Axes
from minions.infra.tiledb.readers.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, ComposeReader, ImageReader

SUPPORTED_FLIP_AXES = [Axes.X, Axes.Y]


class FlipReader(ComposeReader):
    def __init__(self, src_reader: ImageReader, flip_axes: Axes):
        super().__init__(src_reader)
        self.flip_axes = flip_axes

    def level_image(self, level: int, slices: tuple[slice, ...] | None = None):
        if slices is None:
            # If no slices provided, create full slices for all axes
            src_axes = self.src_reader.axes()
            slices = tuple(slice(None) for _ in src_axes.dims)

        # Transform slices to account for flipping
        flipped_slices = self._flip_slices(level, slices)

        # Get the data with flipped slices
        flipped_arr = self.src_reader.level_image(level, flipped_slices)
        return flipped_arr

    def _flip_slices(self, level: int, slices: tuple[slice, ...]) -> tuple[slice, ...]:
        """Transform slices to account for coordinate flipping."""
        src_axes = self.src_reader.axes()
        level_shape = self.src_reader.level_shape(level)

        # Convert slices tuple to list for modification
        flipped_slices = list(slices)

        # Flip slices for each axis that should be flipped
        for flip_axis in self.flip_axes.dims:
            if flip_axis in src_axes.dims:
                axis_idx = src_axes.dims.index(flip_axis)
                if axis_idx < len(flipped_slices):
                    original_slice = flipped_slices[axis_idx]
                    axis_size = level_shape.raw[flip_axis]
                    flipped_slices[axis_idx] = self._flip_single_slice(original_slice, axis_size)

        return tuple(flipped_slices)

    def _flip_single_slice(self, original_slice: slice, axis_size: int) -> slice:
        """Flip a single slice coordinate for a given axis size.

        Example: slice(5,10,2) with axis_size=20 becomes slice(14,9,-2)
        The logic is to map each position in the original slice to its flipped position.
        """
        start, stop, step = original_slice.start, original_slice.stop, original_slice.step

        # Handle None values (default slice behavior)
        if start is None:
            start = 0
        if stop is None:
            stop = axis_size
        if step is None:
            step = 1

        # Handle negative indices
        if start < 0:
            start = axis_size + start
        if stop < 0:
            stop = axis_size + stop

        # Get the actual indices that would be selected by the original slice
        original_indices = list(range(start, stop, step))

        if not original_indices:
            # Empty slice, return empty slice
            return slice(0, 0, 1)

        # Flip each index: index i becomes axis_size - 1 - i
        flipped_indices = [axis_size - 1 - i for i in original_indices]

        # Create a slice that would select these flipped indices
        flipped_start = flipped_indices[0]

        if len(flipped_indices) == 1:
            # Single element, create a slice that selects just this one
            flipped_stop = flipped_start - 1 if flipped_start > 0 else flipped_start + 1
            flipped_step = -1 if flipped_start > 0 else 1
        else:
            # Multiple elements, determine the step
            flipped_step = flipped_indices[1] - flipped_indices[0]
            # Calculate stop: the value just past the last element
            flipped_stop = flipped_indices[-1] + flipped_step

        return slice(flipped_start, flipped_stop, flipped_step)
